using System;
using Oxide.Core;
using Oxide.Core.Plugins;
using Oxide.Core.Libraries.Covalence;

namespace Oxide.Plugins
{
    [Info("Turret Interference Controller", "frizzo420-helper", "1.0.1")]
    [Description("Sets and persists sentry.maxinterference and sentry.interferenceradius, with live commands and permissions.")]
    public class TurretInterferenceController : CovalencePlugin
    {
        private ConfigData config;

        private const string PermAdmin = "turretinterferencecontroller.admin";

        #region Config
        private class ConfigData
        {
            public int MaxInterference = 24;          // raise to allow more active turrets in one area
            public float InterferenceRadius = 40f;    // lower to pack clusters; 0 disables area check
            public bool ApplyOnStartup = true;
            public bool ApplyEveryXMinutes = true;
            public int MinutesInterval = 10;
            public bool WriteCfgAfterApply = true;
        }

        protected override void LoadDefaultConfig()
        {
            config = new ConfigData();
            SaveConfig(config);
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<ConfigData>();
                if (config == null) throw new Exception();
            }
            catch
            {
                PrintWarning("Config file is corrupt or missing, creating a new one with defaults.");
                LoadDefaultConfig();
            }
        }

        protected override void SaveConfig() => SaveConfig(config);
        private void SaveConfig(ConfigData cfg) => Config.WriteObject(cfg, true);
        #endregion

        #region Hooks
        private void Init()
        {
            permission.RegisterPermission(PermAdmin, this);
        }

        private void OnServerInitialized()
        {
            if (config.ApplyOnStartup)
                ApplySettings("startup");

            if (config.ApplyEveryXMinutes && config.MinutesInterval > 0)
                timer.Every(config.MinutesInterval * 60f, () => ApplySettings("timer"));
        }
        #endregion

        #region Logic
        private void ApplySettings(string source)
        {
            Server.Command($"sentry.maxinterference {config.MaxInterference}");
            Server.Command($"sentry.interferenceradius {config.InterferenceRadius}");

            if (config.WriteCfgAfterApply)
                timer.Once(2f, () => Server.Command("writecfg"));

            Puts($"Applied turret settings from {source}: sentry.maxinterference={config.MaxInterference}, sentry.interferenceradius={config.InterferenceRadius}");
        }
        #endregion

        #region Commands
        // Chat command: /turretlimit <max> [radius]
        [Command("turretlimit")]
        private void CmdTurretLimit(IPlayer player, string command, string[] args)
        {
            if (!player.IsServer && !player.HasPermission(PermAdmin))
            {
                player.Message("You lack permission to use this command.");
                return;
            }

            if (args.Length < 1 || args.Length > 2)
            {
                player.Message("Usage: /turretlimit <maxInterference> [radiusMeters]");
                ShowCurrent(player);
                return;
            }

            if (!int.TryParse(args[0], out var max) || max < 0)
            {
                player.Message("Invalid <maxInterference>. Use a non-negative integer.");
                return;
            }

            var radius = config.InterferenceRadius;
            if (args.Length == 2 && !float.TryParse(args[1], out radius))
            {
                player.Message("Invalid [radiusMeters]. Use a number (e.g., 40 or 0).");
                return;
            }

            config.MaxInterference = max;
            config.InterferenceRadius = radius;
            SaveConfig();

            ApplySettings("command");
            player.Message($"Set sentry.maxinterference={max}, sentry.interferenceradius={radius}");
        }

        // Console/RCON: turretlimit.set <max> [radius]
        [Command("turretlimit.set")]
        [Permission("turretinterferencecontroller.admin")] // <-- FIX: provide required permission string
        private void CmdTurretLimitConsole(IPlayer player, string command, string[] args)
        {
            // Allow console regardless; keep perm check for players
            if (player.IsServer)
            {
                CmdTurretLimit(player, command, args);
                return;
            }
            if (!player.HasPermission(PermAdmin))
            {
                player.Reply("No permission.");
                return;
            }
            CmdTurretLimit(player, command, args);
        }

        [Command("turretlimit.show")]
        private void CmdTurretShow(IPlayer player, string command, string[] args)
        {
            if (!player.IsServer && !player.HasPermission(PermAdmin))
            {
                player.Message("No permission.");
                return;
            }
            ShowCurrent(player);
        }

        private void ShowCurrent(IPlayer player)
        {
            player.Message($"Current (config): maxInterference={config.MaxInterference}, radius={config.InterferenceRadius}m");
            player.Message("Tip: Set radius to 0 to disable the area check entirely.");
        }
        #endregion
    }
}
