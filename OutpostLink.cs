using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;

namespace Oxide.Plugins
{
    [Info("OutpostLink", "illRalli", "2.0.2")]
    [Description("Connects web API and RUST server for Steam to Discord linking")]
    class OutpostLink : CovalencePlugin
    {
        #region Variables

        private List<RoleEntry> _apiRoles = new List<RoleEntry>();

        private string _linkedGroupName;
        private string _steamGroupName;
        private string _boosterGroupName;

        // Added: longer web timeout to avoid sporadic code:0 timeouts
        const float REQUEST_TIMEOUT = 20f; // bump to 30f if you still see timeouts

        private class RoleEntry
        {
            public string Name  { get; set; }
            public string Value { get; set; }
        }

        private class SyncResponse
        {
            [JsonProperty("isLinked")]
            public int IsLinked { get; set; }

            [JsonProperty("isSteamGroupMember")]
            public int IsSteamGroupMember { get; set; }

            [JsonProperty("isBooster")]
            public int IsBooster { get; set; }
        }

        #endregion

        #region Configuration

        private PluginConfig config;

        class PluginConfig
        {
            [JsonProperty(PropertyName = "Commands")]
            public Commands Commands { get; set; }

            [JsonProperty(PropertyName = "Linking")]
            public Linking Linking { get; set; }

            [JsonProperty(PropertyName = "Console")]
            public ConsoleConfig Console { get; set; }
        }

        class Commands
        {
            [JsonProperty(PropertyName = "Link Command")]
            public string linkCommand { get; set; }
        }

        class Linking
        {
            [JsonProperty(PropertyName = "Link URL")]
            public string linkURL { get; set; }

            [JsonProperty(PropertyName = "Api URL")]
            public string apiURL { get; set; }

            [JsonProperty(PropertyName = "Secret Key")]
            public string webSecret { get; set; }

            [JsonProperty(PropertyName = "Server ID")]
            public string serverID { get; set; }
        }

        class ConsoleConfig
        {
            [JsonProperty(PropertyName = "Advanced Logging (Debug)")]
            public bool advancedLogging { get; set; }
        }

        #endregion

        #region Oxide Hooks

        private void OnServerInitialized()
        {
            LoadVariables();
            GetServerRoles(); // Grabs the role names once
            AddCovalenceCommand(config.Commands.linkCommand, "LinkUser");
        }

        private void OnUserConnected(IPlayer player)
        {
            DebugLog($"[OnUserConnected] Syncing roles for {player.Id}...");
            ProcessPlayerStatus(player);
        }

        #endregion

        #region Main Sync Logic
        
        private void LinkUser(IPlayer player, string command, string[] args)
        {
            ProcessPlayerStatus(player);
        }

        private void ProcessPlayerStatus(IPlayer player)
        {
            string statusUrl = $"{config.Linking.apiURL}?secret={config.Linking.webSecret}&function=getPlayerStatus&steamid={player.Id}";
            DebugLog($"Checking player status: {statusUrl}");
            
            // CHANGED: use helper with timeout instead of EnqueueGet
            EnqueueGetWithTimeout(statusUrl, (code, response) =>
            {
                DebugLog($"Code: {code}");
                DebugLog($"Response: {response}");

                if (!IsValidResponse(code, response))
                {
                    player.Reply("Unable to verify link status at this time. Please try again later.");
                    return;
                }

                try
                {
                    var status = JsonConvert.DeserializeObject<SyncResponse>(response);
                    if (status == null)
                    {
                        player.Reply("Invalid response received from server.");
                        return;
                    }
                                            
                    EnsureGroup(player, _linkedGroupName, status.IsLinked == 1);

                    if (_steamGroupName != null)
                        EnsureGroup(player, _steamGroupName, status.IsSteamGroupMember == 1);

                    if (_boosterGroupName != null)
                        EnsureGroup(player, _boosterGroupName, status.IsBooster == 1);

                    if (status.IsLinked == 1)
                    {
                        player.Reply(lang.GetMessage("AccountFound", this, player.Id));
                    }
                    else
                    {
                        player.Reply(lang.GetMessage("AccountNotFound", this, player.Id));
                        player.Reply(string.Format(lang.GetMessage("LinkMethodsMessage", this, player.Id), config.Linking.linkURL));
                    }
                }
                catch (Exception ex)
                {
                    PrintWarning($"[ProcessPlayerStatus] Error deserializing SyncResponse: {ex.Message}");
                    player.Reply("An error occurred while processing your request.");
                }
            });
        }

        private void EnsureGroup(IPlayer player, string groupName, bool shouldBelong)
        {
            if (string.IsNullOrEmpty(groupName)) return;

            bool isInGroup = player.BelongsToGroup(groupName);
            if (shouldBelong && !isInGroup)
            {
                player.AddToGroup(groupName);
                DebugLog($"Added {player.Id} to group '{groupName}'");
            }
            else if (!shouldBelong && isInGroup)
            {
                player.RemoveFromGroup(groupName);
                DebugLog($"Removed {player.Id} from group '{groupName}'");
            }
        }

        #endregion

        #region Web Helpers

        // NEW: helper to enforce our timeout on all GETs
        private void EnqueueGetWithTimeout(string url, Action<int, string> callback)
        {
            webrequest.Enqueue(
				url,
				null,                   // no body
				callback,
				this,
				RequestMethod.GET,      // <-- use enum not string
				null,                   // no headers
				REQUEST_TIMEOUT         // timeout in seconds
		);

        }

        private bool IsValidResponse(int code, string response)
        {
            if (code != 200 || string.IsNullOrEmpty(response))
            {
                if (config.Console.advancedLogging)
                    PrintWarning($"Invalid response code: {code}, response: {response}");
                return false;
            }
            return true;
        }

        private void DebugLog(string message)
        {
            if (config.Console.advancedLogging)
                PrintWarning(message);
        }

        #endregion

        #region Roles Setup

        private void GetServerRoles()
        {
            string url = $"{config.Linking.apiURL}?secret={config.Linking.webSecret}&function=getServerGroups&server={config.Linking.serverID}";

            // CHANGED: use helper with timeout instead of EnqueueGet
            EnqueueGetWithTimeout(url, (code, response) =>
            {
                if (!IsValidResponse(code, response)) return;

                try
                {
                    var data = JsonConvert.DeserializeObject<Dictionary<string, string>>(response);
                    if (data == null)
                    {
                        PrintWarning("getServerGroups responded with null data");
                        return;
                    }

                    _linkedGroupName  = data.ContainsKey("linkedGroup") ? data["linkedGroup"] : null;
                    _steamGroupName   = data.ContainsKey("steamgroupGroup") ? data["steamgroupGroup"] : null;
                    _boosterGroupName = data.ContainsKey("boosterGroup") ? data["boosterGroup"] : null;

                    _apiRoles = data.Select(kv => new RoleEntry
                    {
                        Name = kv.Key,
                        Value = kv.Value
                    }).ToList();

                    CheckForGroups();
                }
                catch (Exception ex)
                {
                    PrintWarning($"Error parsing response: {ex.Message}");
                }
            });
        }

        private void CheckForGroups()
        {
            foreach (var role in _apiRoles)
            {
                var permissionGroup = role.Value;
                if (string.IsNullOrEmpty(permissionGroup)) continue;

                if (!permission.GroupExists(permissionGroup))
                {
                    DebugLog($"Group '{permissionGroup}' not found. You may want to create it.");
                }
                else
                {
                    DebugLog($"Group '{permissionGroup}' found.");
                }
            }
        }

        #endregion

        #region Configuration + Messages

        private void LoadVariables()
        {
            LoadConfigVariables();
            SaveConfig();
        }

        protected override void LoadDefaultConfig()
        {
            var configVars = new PluginConfig
            {
                Commands = new Commands
                {
                    linkCommand = "link",
                },
                Linking = new Linking
                {
                    linkURL = "",
                    apiURL = "",
                    webSecret = "",
                    serverID = "1",
                },
                Console = new ConsoleConfig
                {
                    advancedLogging = false
                }
            };
            SaveConfig(configVars);
        }

        private void LoadConfigVariables() => config = Config.ReadObject<PluginConfig>();
        void SaveConfig(PluginConfig configVars) => Config.WriteObject(configVars, true);

        private new void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["AccountNotFound"]       = "Linked account not found.",
                ["AccountFound"]          = "Linked account found!",
                ["HasLinkedMessage"]      = "Your accounts have been linked!",
                ["VerifyAccountMessage"]  = "Use /{0} <Discord ID#> to verify your account",
                ["VerifyErrorMessage"]    = "Discord ID does not match. Contact an Admin.",
                ["LinkMethodsMessage"]    = "Link your account at {0}!"
            }, this);
        }

        #endregion
    }
}
